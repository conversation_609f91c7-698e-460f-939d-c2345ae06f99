//! Orca DEX适配器
//!
//! 支持Orca Whirlpool协议的完整交换功能
//! 包含Concentrated Liquidity管理、价格oracle集成和tick数组处理

use anchor_lang::prelude::*;
use anchor_spl::token::TokenAccount;
use crate::adapters::common::{DexProcessor, SwapAccountIndices};
use crate::routing::{Route, Dex};
use crate::error::RouteError;

// 自实现Orca相关类型和指令构建器
use solana_program::{
    instruction::Instruction,
    program::invoke_signed,
    pubkey::Pubkey,
};

/// Orca Whirlpool程序ID
pub const WHIRLPOOL_PROGRAM_ID: Pubkey = solana_program::pubkey!("whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc");

/// Orca Whirlpool适配器
///
/// 实现Orca Whirlpool Concentrated Liquidity Market Maker的交换逻辑
/// 支持tick数组管理、价格oracle和多级费率
pub struct OrcaProcessor {
    /// 最大滑点容忍度（基点）
    pub max_slippage_bps: u16,
    /// 价格影响阈值（基点）
    pub max_price_impact_bps: u16,
    /// Oracle价格偏差阈值（基点）
    pub max_oracle_deviation_bps: u16,
}

impl Default for OrcaProcessor {
    fn default() -> Self {
        Self {
            max_slippage_bps: 100, // 1%
            max_price_impact_bps: 300, // 3%
            max_oracle_deviation_bps: 500, // 5%
        }
    }
}

/// Whirlpool交换参数
#[derive(Debug, Clone)]
pub struct WhirlpoolSwapParams {
    pub whirlpool: Pubkey,
    pub token_vault_a: Pubkey,
    pub token_vault_b: Pubkey,
    pub token_mint_a: Pubkey,
    pub token_mint_b: Pubkey,
    pub oracle: Pubkey,
    pub tick_arrays: Vec<Pubkey>,
    pub sqrt_price_limit: u128,
    pub amount_specified: u64,
    pub other_amount_threshold: u64,
    pub sqrt_price_limit_x64: u128,
    pub a_to_b: bool,
}

impl DexProcessor for OrcaProcessor {
    fn execute_swap_cpi<'info>(
        &self,
        route: &Route,
        accounts: &[AccountInfo<'info>],
        amount_in: u64,
        min_amount_out: u64,
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<u64> {
        msg!("执行Orca Whirlpool交换: {} -> {}, 最小输出: {}", amount_in, min_amount_out, min_amount_out);

        // 解析Whirlpool交换参数
        let swap_params = self.parse_whirlpool_swap_params(&route.swap_data)?;

        // 预估输出并验证滑点
        let estimated_output = self.estimate_whirlpool_output(
            &swap_params,
            amount_in,
        )?;

        if estimated_output < min_amount_out {
            return Err(RouteError::SlippageTooHigh.into());
        }

        // 验证价格impact
        self.validate_price_impact(&swap_params, amount_in, estimated_output)?;

        // 构建交换指令
        let swap_instruction = self.build_whirlpool_swap_instruction(
            &swap_params,
            accounts,
            amount_in,
            min_amount_out,
        )?;

        // 执行CPI调用
        self.execute_whirlpool_cpi(&swap_instruction, accounts, owner_seeds)?;

        Ok(estimated_output)
    }

    fn validate_accounts(
        &self,
        route: &Route,
        accounts: &[AccountInfo],
        hop_index: usize,
    ) -> Result<()> {
        // 验证账户数量（基础12个账户 + tick arrays）
        if accounts.len() < 12 {
            msg!("Orca Whirlpool需要至少12个账户，实际: {}", accounts.len());
            return Err(RouteError::InvalidAccountCount.into());
        }

        // 解析交换参数
        let swap_params = self.parse_whirlpool_swap_params(&route.swap_data)?;

        // 验证关键账户
        let indices = self.get_account_indices(accounts)?;

        // 验证用户签名权限
        if hop_index == 0 && !accounts[indices.swap_authority].is_signer {
            return Err(RouteError::SwapAuthorityIsNotSigner.into());
        }

        // 验证源代币账户
        let source_token_account = &accounts[indices.source_token_account];
        let source_token_data = TokenAccount::try_deserialize(
            &mut source_token_account.data.borrow().as_ref()
        ).map_err(|_| RouteError::InvalidDexAccounts)?;

        if source_token_data.mint != route.input_mint {
            msg!("源代币mint不匹配: 期望 {}, 实际 {}", route.input_mint, source_token_data.mint);
            return Err(RouteError::TokenAccountMintMismatch.into());
        }

        // 验证目标代币账户
        let dest_token_account = &accounts[indices.destination_token_account];
        let dest_token_data = TokenAccount::try_deserialize(
            &mut dest_token_account.data.borrow().as_ref()
        ).map_err(|_| RouteError::InvalidDexAccounts)?;

        if dest_token_data.mint != route.output_mint {
            msg!("目标代币mint不匹配: 期望 {}, 实际 {}", route.output_mint, dest_token_data.mint);
            return Err(RouteError::TokenAccountMintMismatch.into());
        }

        // 验证whirlpool账户
        if let Some(pool_idx) = indices.pool_account {
            if *accounts[pool_idx].key != swap_params.whirlpool {
                msg!("Whirlpool账户不匹配: 期望 {}, 实际 {}", swap_params.whirlpool, accounts[pool_idx].key);
                return Err(RouteError::InvalidDexAccounts.into());
            }
        }

        msg!("Orca Whirlpool账户验证通过: {} -> {}", route.input_mint, route.output_mint);
        Ok(())
    }

    fn get_account_indices(
        &self,
        accounts: &[AccountInfo],
    ) -> Result<SwapAccountIndices> {
        // Orca Whirlpool的标准账户布局
        // 0: token_program
        // 1: token_owner_account_a
        // 2: token_vault_a
        // 3: token_owner_account_b
        // 4: token_vault_b
        // 5: whirlpool
        // 6: swap_authority (signer)
        // 7: oracle
        // 8+: tick_arrays

        if accounts.len() < 12 {
            return Err(RouteError::InvalidAccountCount.into());
        }

        let mut additional_accounts = vec![0, 2, 4, 7]; // 固定的额外账户

        // 添加所有tick array账户
        for i in 8..accounts.len() {
            additional_accounts.push(i);
        }

        Ok(SwapAccountIndices {
            source_token_account: 1,
            destination_token_account: 3,
            swap_authority: 6,
            dex_program: 0, // token_program作为主要程序引用
            pool_account: Some(5),
            additional_accounts,
        })
    }

    fn dex_name(&self) -> &'static str {
        "Orca Whirlpool"
    }

    fn dex_type(&self) -> Dex {
        Dex::Orca
    }

    fn estimate_gas_cost(&self, _route: &Route) -> u64 {
        // Whirlpool需要更多计算资源处理concentrated liquidity
        160_000
    }
}

impl OrcaProcessor {
    /// 解析Whirlpool交换参数
    fn parse_whirlpool_swap_params(&self, swap_data: &[u8]) -> Result<WhirlpoolSwapParams> {
        if swap_data.is_empty() {
            return Err(RouteError::InvalidRouteConfig.into());
        }

        WhirlpoolSwapParams::try_from_slice(swap_data)
            .map_err(|e| {
                msg!("解析Whirlpool交换参数失败: {:?}", e);
                RouteError::InvalidRouteConfig.into()
            })
    }

    /// 预估Whirlpool交换输出
    fn estimate_whirlpool_output(
        &self,
        params: &WhirlpoolSwapParams,
        amount_in: u64,
    ) -> Result<u64> {
        // 简化的Whirlpool输出估算
        // 实际应该基于当前sqrt_price和tick数据计算

        // 基础费率（0.3%用于演示，实际应该从pool配置读取）
        let fee_rate = 300; // 0.3% in basis points
        let fee_amount = amount_in
            .checked_mul(fee_rate)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(100_000)
            .ok_or(RouteError::DivisionByZero)?;

        let amount_after_fee = amount_in
            .checked_sub(fee_amount)
            .ok_or(RouteError::MathUnderflow)?;

        // 简化的价格计算（实际应该使用sqrt price math）
        let estimated_output = if params.a_to_b {
            // A -> B的交换
            amount_after_fee
        } else {
            // B -> A的交换
            amount_after_fee
        };

        msg!("预估Whirlpool输出: 输入 {} -> 输出 {}, 费用 {}",
            amount_in, estimated_output, fee_amount);

        Ok(estimated_output)
    }

    /// 验证价格影响
    fn validate_price_impact(
        &self,
        _params: &WhirlpoolSwapParams,
        amount_in: u64,
        estimated_out: u64,
    ) -> Result<()> {
        if amount_in == 0 {
            return Ok(());
        }

        // 简化的价格影响计算
        // 实际应该基于流动性分布和当前tick位置
        let price_impact = if estimated_out > 0 {
            let price_change = amount_in
                .checked_mul(10000)
                .ok_or(RouteError::MathOverflow)?
                .checked_div(estimated_out)
                .ok_or(RouteError::DivisionByZero)?;

            // 计算价格影响百分比
            if price_change > 10000 {
                price_change - 10000
            } else {
                0
            }
        } else {
            10000 // 如果没有输出，价格影响为100%
        };

        if price_impact as u16 > self.max_price_impact_bps {
            msg!("价格影响过高: {}基点 > {}基点", price_impact, self.max_price_impact_bps);
            return Err(RouteError::PriceImpactTooHigh.into());
        }

        Ok(())
    }

    /// 构建Whirlpool交换指令
    fn build_whirlpool_swap_instruction(
        &self,
        params: &WhirlpoolSwapParams,
        accounts: &[AccountInfo],
        amount_in: u64,
        min_amount_out: u64,
    ) -> Result<Instruction> {
        // Whirlpool swap指令的discriminator
        let discriminator = [0x2c, 0x1c, 0x9f, 0xe8, 0x26, 0x04, 0x1c, 0xeb];

        // 构建指令数据
        let mut instruction_data = Vec::new();
        instruction_data.extend_from_slice(&discriminator);

        // Swap指令参数（简化版本）
        instruction_data.extend_from_slice(&amount_in.to_le_bytes());
        instruction_data.extend_from_slice(&min_amount_out.to_le_bytes());
        instruction_data.extend_from_slice(&params.sqrt_price_limit_x64.to_le_bytes());
        instruction_data.push(params.a_to_b as u8);

        // 构建账户元数据
        let mut account_metas = Vec::new();

        // 基础账户
        account_metas.extend_from_slice(&[
            anchor_lang::solana_program::instruction::AccountMeta::new_readonly(
                *accounts[0].key, false), // token_program
            anchor_lang::solana_program::instruction::AccountMeta::new(
                *accounts[1].key, false), // token_owner_account_a
            anchor_lang::solana_program::instruction::AccountMeta::new(
                *accounts[2].key, false), // token_vault_a
            anchor_lang::solana_program::instruction::AccountMeta::new(
                *accounts[3].key, false), // token_owner_account_b
            anchor_lang::solana_program::instruction::AccountMeta::new(
                *accounts[4].key, false), // token_vault_b
            anchor_lang::solana_program::instruction::AccountMeta::new(
                *accounts[5].key, false), // whirlpool
            anchor_lang::solana_program::instruction::AccountMeta::new_readonly(
                *accounts[6].key, true),  // swap_authority
            anchor_lang::solana_program::instruction::AccountMeta::new_readonly(
                *accounts[7].key, false), // oracle
        ]);

        // 添加tick arrays
        for i in 8..accounts.len() {
            account_metas.push(
                anchor_lang::solana_program::instruction::AccountMeta::new(
                    *accounts[i].key, false)
            );
        }

        Ok(Instruction {
            program_id: WHIRLPOOL_PROGRAM_ID,
            accounts: account_metas,
            data: instruction_data,
        })
    }

    /// 执行Whirlpool CPI调用
    fn execute_whirlpool_cpi<'info>(
        &self,
        instruction: &Instruction,
        accounts: &[AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<()> {
        // 验证指令账户数量
        if instruction.accounts.len() > accounts.len() {
            msg!("账户数量不足: 需要 {}, 提供 {}", instruction.accounts.len(), accounts.len());
            return Err(RouteError::InvalidAccountCount.into());
        }

        // 映射账户到指令
        let mut account_infos = Vec::with_capacity(instruction.accounts.len());
        for i in 0..instruction.accounts.len() {
            if i < accounts.len() {
                account_infos.push(accounts[i].clone());
            } else {
                return Err(RouteError::InvalidAccountCount.into());
            }
        }

        // 记录CPI调用详情
        msg!("执行Orca Whirlpool CPI: 程序 {}, 账户数 {}",
            instruction.program_id, account_infos.len());

        // 执行CPI调用
        match owner_seeds {
            Some(seeds) => {
                invoke_signed(instruction, &account_infos, seeds)
                    .map_err(|e| {
                        msg!("Orca Whirlpool CPI调用失败 (带签名): {:?}", e);
                        RouteError::DexOperationFailed
                    })?
            }
            None => {
                solana_program::program::invoke(instruction, &account_infos)
                    .map_err(|e| {
                        msg!("Orca Whirlpool CPI调用失败: {:?}", e);
                        RouteError::DexOperationFailed
                    })?
            }
        }

        msg!("Orca Whirlpool CPI调用成功");
        Ok(())
    }

    /// 创建Whirlpool交换参数
    pub fn create_whirlpool_swap_params(
        whirlpool: Pubkey,
        token_vault_a: Pubkey,
        token_vault_b: Pubkey,
        token_mint_a: Pubkey,
        token_mint_b: Pubkey,
        oracle: Pubkey,
        tick_arrays: Vec<Pubkey>,
        sqrt_price_limit: u128,
        amount_specified: u64,
        other_amount_threshold: u64,
        sqrt_price_limit_x64: u128,
        a_to_b: bool,
    ) -> WhirlpoolSwapParams {
        WhirlpoolSwapParams {
            whirlpool,
            token_vault_a,
            token_vault_b,
            token_mint_a,
            token_mint_b,
            oracle,
            tick_arrays,
            sqrt_price_limit,
            amount_specified,
            other_amount_threshold,
            sqrt_price_limit_x64,
            a_to_b,
        }
    }

    /// 计算tick数组索引
    pub fn calculate_tick_array_indices(
        &self,
        current_tick: i32,
        tick_spacing: u16,
        direction: bool,
    ) -> Result<Vec<i32>> {
        let mut indices = Vec::new();

        // 计算当前tick array索引
        let current_array_index = current_tick / (tick_spacing as i32 * 88);
        indices.push(current_array_index);

        // 根据交换方向添加相邻的tick arrays
        if direction {
            // 向上交换，添加下一个tick array
            indices.push(current_array_index + 1);
        } else {
            // 向下交换，添加前一个tick array
            indices.push(current_array_index - 1);
        }

        Ok(indices)
    }

    /// 验证Oracle价格
    pub fn validate_oracle_price(
        &self,
        oracle_price: u64,
        current_price: u64,
    ) -> Result<()> {
        if oracle_price == 0 || current_price == 0 {
            return Ok(()); // 跳过验证如果价格为0
        }

        let price_diff = if oracle_price > current_price {
            oracle_price - current_price
        } else {
            current_price - oracle_price
        };

        let deviation_bps = price_diff
            .checked_mul(10000)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(current_price)
            .ok_or(RouteError::DivisionByZero)?;

        if deviation_bps as u16 > self.max_oracle_deviation_bps {
            msg!("Oracle价格偏差过大: {}基点 > {}基点", deviation_bps, self.max_oracle_deviation_bps);
            return Err(RouteError::PriceCalculationError.into());
        }

        Ok(())
    }
}

/// Orca工具函数
pub mod utils {
    use super::*;

    /// 计算sqrt price from tick
    pub fn sqrt_price_from_tick(tick: i32) -> Result<u128> {
        // 简化的sqrt price计算
        // 实际Whirlpool使用更复杂的数学库
        let base_price = 1.0001_f64.powi(tick);
        let sqrt_price = base_price.sqrt();

        // 转换为Q64.64格式
        let q64_price = (sqrt_price * (2_u128.pow(64) as f64)) as u128;

        Ok(q64_price)
    }

    /// 计算tick from sqrt price
    pub fn tick_from_sqrt_price(sqrt_price_x64: u128) -> Result<i32> {
        if sqrt_price_x64 == 0 {
            return Err(RouteError::DivisionByZero.into());
        }

        // 转换回价格
        let price = (sqrt_price_x64 as f64) / (2_u128.pow(64) as f64);
        let price_squared = price * price;

        // 计算tick
        let tick = price_squared.log(1.0001);

        Ok(tick as i32)
    }

    /// 验证Whirlpool地址格式
    pub fn validate_whirlpool_address(whirlpool: &Pubkey) -> Result<()> {
        if whirlpool == &Pubkey::default() {
            return Err(RouteError::InvalidRouteConfig.into());
        }
        Ok(())
    }

    /// 计算fee tier rate
    pub fn get_fee_tier_rate(fee_tier: u16) -> Result<u16> {
        match fee_tier {
            1 => Ok(1),      // 0.01%
            5 => Ok(5),      // 0.05%
            30 => Ok(30),    // 0.3%
            65 => Ok(65),    // 0.65%
            100 => Ok(100),  // 1%
            _ => Err(RouteError::InvalidRouteConfig.into()),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use solana_sdk::pubkey::Pubkey;

    #[test]
    fn test_whirlpool_processor_creation() {
        let processor = OrcaProcessor::default();
        assert_eq!(processor.max_slippage_bps, 100);
        assert_eq!(processor.max_price_impact_bps, 300);
        assert_eq!(processor.max_oracle_deviation_bps, 500);
        assert_eq!(processor.dex_name(), "Orca Whirlpool");
        assert_eq!(processor.dex_type(), Dex::Orca);
    }

    #[test]
    fn test_whirlpool_swap_params_serialization() {
        let params = WhirlpoolSwapParams {
            whirlpool: Pubkey::new_unique(),
            token_vault_a: Pubkey::new_unique(),
            token_vault_b: Pubkey::new_unique(),
            token_mint_a: Pubkey::new_unique(),
            token_mint_b: Pubkey::new_unique(),
            oracle: Pubkey::new_unique(),
            tick_arrays: vec![Pubkey::new_unique(), Pubkey::new_unique()],
            sqrt_price_limit: 0,
            amount_specified: 1_000_000,
            other_amount_threshold: 950_000,
            sqrt_price_limit_x64: 0,
            a_to_b: true,
        };

        let serialized = params.try_to_vec().unwrap();
        let deserialized = WhirlpoolSwapParams::try_from_slice(&serialized).unwrap();

        assert_eq!(params.whirlpool, deserialized.whirlpool);
        assert_eq!(params.a_to_b, deserialized.a_to_b);
        assert_eq!(params.tick_arrays.len(), deserialized.tick_arrays.len());
    }

    #[test]
    fn test_whirlpool_output_estimation() {
        let processor = OrcaProcessor::default();
        let params = WhirlpoolSwapParams {
            whirlpool: Pubkey::new_unique(),
            token_vault_a: Pubkey::new_unique(),
            token_vault_b: Pubkey::new_unique(),
            token_mint_a: Pubkey::new_unique(),
            token_mint_b: Pubkey::new_unique(),
            oracle: Pubkey::new_unique(),
            tick_arrays: vec![Pubkey::new_unique()],
            sqrt_price_limit: 0,
            amount_specified: 1_000_000,
            other_amount_threshold: 950_000,
            sqrt_price_limit_x64: 0,
            a_to_b: true,
        };

        let amount_in = 1_000_000;
        let output = processor.estimate_whirlpool_output(&params, amount_in).unwrap();

        // 应该扣除费用
        assert!(output < amount_in);
        assert!(output > 0);
    }

    #[test]
    fn test_tick_array_indices_calculation() {
        let processor = OrcaProcessor::default();

        // 测试向上交换
        let indices = processor.calculate_tick_array_indices(1000, 64, true).unwrap();
        assert_eq!(indices.len(), 2);
        assert!(indices[1] > indices[0]); // 下一个索引应该更大

        // 测试向下交换
        let indices = processor.calculate_tick_array_indices(1000, 64, false).unwrap();
        assert_eq!(indices.len(), 2);
        assert!(indices[1] < indices[0]); // 前一个索引应该更小
    }

    #[test]
    fn test_oracle_price_validation() {
        let processor = OrcaProcessor::default();

        // 正常价格偏差
        assert!(processor.validate_oracle_price(100_000, 101_000).is_ok());

        // 过大的价格偏差
        assert!(processor.validate_oracle_price(100_000, 200_000).is_err());
    }

    #[test]
    fn test_utils_functions() {
        // 测试sqrt price计算
        let sqrt_price = utils::sqrt_price_from_tick(0).unwrap();
        assert!(sqrt_price > 0);

        // 测试tick计算
        let tick = utils::tick_from_sqrt_price(sqrt_price).unwrap();
        assert_eq!(tick, 0); // 应该回到原始tick

        // 测试fee tier
        assert_eq!(utils::get_fee_tier_rate(30).unwrap(), 30);
        assert!(utils::get_fee_tier_rate(999).is_err());

        // 测试地址验证
        assert!(utils::validate_whirlpool_address(&Pubkey::default()).is_err());
        assert!(utils::validate_whirlpool_address(&Pubkey::new_unique()).is_ok());
    }
}
