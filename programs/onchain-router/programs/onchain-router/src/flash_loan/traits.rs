//! 闪电贷提供者通用接口定义
//!
//! 定义了所有闪电贷提供者必须实现的核心trait

use anchor_lang::prelude::*;
use crate::error::RouteError;

/// 闪电贷提供者的核心trait
/// 
/// 所有闪电贷协议适配器都必须实现这个trait
pub trait FlashLoanProvider {
    /// 获取提供者名称
    fn get_provider_name(&self) -> &'static str;
    
    /// 获取指定代币的最大可借贷数量
    fn get_max_loan_amount(&self, mint: &Pubkey) -> Result<u64>;
    
    /// 计算闪电贷费用
    /// 
    /// # 参数
    /// - `amount`: 借贷数量
    /// 
    /// # 返回
    /// 返回需要支付的费用（不包括本金）
    fn calculate_fee(&self, amount: u64) -> Result<u64>;
    
    /// 获取费率（基点形式）
    /// 
    /// # 返回
    /// 返回费率，以基点表示（如 30 = 0.3%）
    fn get_fee_bps(&self) -> u16;
    
    /// 发起闪电贷
    /// 
    /// # 参数
    /// - `accounts`: 相关账户信息
    /// - `amount`: 借贷数量
    /// - `callback_data`: 回调数据，用于在还款回调中执行套利逻辑
    /// 
    /// # 安全性
    /// 这个方法必须确保：
    /// 1. 验证借贷数量不超过最大限制
    /// 2. 正确计算费用
    /// 3. 确保回调执行的原子性
    fn initiate_flash_loan<'info>(
        &self,
        accounts: &[AccountInfo<'info>],
        amount: u64,
        mint: &Pubkey,
        callback_data: &[u8],
    ) -> Result<FlashLoanContext>;
    
    /// 验证还款
    /// 
    /// # 参数
    /// - `amount`: 借贷本金
    /// - `fee`: 费用
    /// 
    /// # 返回
    /// 验证通过返回Ok，否则返回错误
    fn validate_repayment(&self, amount: u64, fee: u64) -> Result<()>;
    
    /// 获取还款截止时间（相对于发起时间的slot数）
    fn get_repayment_deadline_slots(&self) -> u64 {
        1 // 大部分闪电贷要求在同一个交易内还款
    }
    
    /// 检查提供者是否支持指定的代币
    fn supports_mint(&self, mint: &Pubkey) -> bool;
    
    /// 获取提供者的程序ID
    fn get_program_id(&self) -> Pubkey;
}

/// 闪电贷执行上下文
/// 
/// 存储闪电贷执行过程中的关键信息
#[derive(Debug, Clone)]
pub struct FlashLoanContext {
    /// 借贷金额
    pub amount: u64,
    /// 费用
    pub fee: u64,
    /// 代币mint
    pub mint: Pubkey,
    /// 提供者名称
    pub provider: &'static str,
    /// 发起时间戳
    pub initiated_at: i64,
    /// 还款截止时间戳
    pub deadline: i64,
}

impl FlashLoanContext {
    /// 创建新的闪电贷上下文
    pub fn new(
        amount: u64,
        fee: u64,
        mint: Pubkey,
        provider: &'static str,
        deadline_slots: u64,
    ) -> Result<Self> {
        let clock = Clock::get()?;
        let initiated_at = clock.unix_timestamp;
        let deadline = initiated_at + (deadline_slots as i64 * 400_000_000); // 大约每个slot 400ms
        
        Ok(Self {
            amount,
            fee,
            mint,
            provider,
            initiated_at,
            deadline,
        })
    }
    
    /// 检查是否已超时
    pub fn is_expired(&self) -> Result<bool> {
        let current_time = Clock::get()?.unix_timestamp;
        Ok(current_time > self.deadline)
    }
    
    /// 计算总还款金额（本金+费用）
    pub fn total_repayment(&self) -> u64 {
        self.amount.saturating_add(self.fee)
    }
}

/// 闪电贷回调trait
/// 
/// 定义闪电贷回调处理的接口
pub trait FlashLoanCallback {
    /// 执行闪电贷回调
    /// 
    /// # 参数
    /// - `context`: 闪电贷上下文
    /// - `accounts`: 相关账户
    /// - `callback_data`: 回调数据
    /// 
    /// # 返回
    /// 返回执行结果，通常是利润金额
    fn execute_callback<'info>(
        &self,
        context: &FlashLoanContext,
        accounts: &[AccountInfo<'info>],
        callback_data: &[u8],
    ) -> Result<u64>;
    
    /// 验证回调数据的有效性
    fn validate_callback_data(&self, data: &[u8]) -> Result<()>;
}

/// 闪电贷提供者管理器trait
/// 
/// 管理多个闪电贷提供者，提供统一的接口
pub trait FlashLoanManager {
    /// 获取最优的闪电贷提供者
    /// 
    /// # 参数
    /// - `amount`: 借贷数量
    /// - `mint`: 代币mint
    /// 
    /// # 返回
    /// 返回最优的提供者，通常基于费率和可用性
    fn get_best_provider(&self, amount: u64, mint: &Pubkey) -> Result<Box<dyn FlashLoanProvider>>;
    
    /// 获取所有支持指定代币的提供者
    fn get_providers_for_mint(&self, mint: &Pubkey) -> Vec<Box<dyn FlashLoanProvider>>;
    
    /// 获取所有注册的提供者
    fn get_all_providers(&self) -> Vec<Box<dyn FlashLoanProvider>>;
    
    /// 检查提供者是否在白名单中
    fn is_provider_whitelisted(&self, program_id: &Pubkey) -> bool;
}

/// 闪电贷执行统计信息
#[derive(Debug, Clone, Default)]
pub struct FlashLoanStats {
    /// 总执行次数
    pub total_executions: u64,
    /// 成功次数
    pub successful_executions: u64,
    /// 总借贷金额
    pub total_borrowed: u64,
    /// 总费用支付
    pub total_fees_paid: u64,
    /// 总利润
    pub total_profit: u64,
    /// 平均执行时间（毫秒）
    pub average_execution_time_ms: u64,
}

impl FlashLoanStats {
    /// 计算成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_executions == 0 {
            0.0
        } else {
            self.successful_executions as f64 / self.total_executions as f64
        }
    }
    
    /// 计算平均费率（基点）
    pub fn average_fee_bps(&self) -> u16 {
        if self.total_borrowed == 0 {
            0
        } else {
            ((self.total_fees_paid * 10000) / self.total_borrowed) as u16
        }
    }
    
    /// 更新统计信息
    pub fn update(&mut self, success: bool, amount: u64, fee: u64, profit: u64, execution_time_ms: u64) {
        self.total_executions += 1;
        if success {
            self.successful_executions += 1;
            self.total_borrowed += amount;
            self.total_fees_paid += fee;
            self.total_profit += profit;
        }
        
        // 更新平均执行时间（简单的移动平均）
        self.average_execution_time_ms = 
            (self.average_execution_time_ms * (self.total_executions - 1) + execution_time_ms) 
            / self.total_executions;
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use solana_program::pubkey::Pubkey;

    #[test]
    fn test_flash_loan_context_creation() {
        let amount = 1000_000_000; // 1 token
        let fee = 10_000_000; // 0.01 token (1% fee)
        let mint = Pubkey::new_unique();
        let provider = "TestProvider";
        let deadline_slots = 1;
        
        // 注意：这个测试在实际Solana环境外会失败，因为Clock::get()不可用
        // 在实际测试中应该使用mock或者集成测试
    }
    
    #[test]
    fn test_flash_loan_stats() {
        let mut stats = FlashLoanStats::default();
        
        // 模拟几次执行
        stats.update(true, 1000, 10, 50, 100);  // 成功
        stats.update(false, 2000, 0, 0, 150);   // 失败
        stats.update(true, 1500, 15, 75, 120);  // 成功
        
        assert_eq!(stats.total_executions, 3);
        assert_eq!(stats.successful_executions, 2);
        assert_eq!(stats.success_rate(), 2.0 / 3.0);
        assert_eq!(stats.total_borrowed, 2500); // 只计算成功的
        assert_eq!(stats.total_fees_paid, 25);
        assert_eq!(stats.total_profit, 125);
    }
}